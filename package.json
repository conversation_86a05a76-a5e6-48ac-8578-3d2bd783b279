{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@datadog/browser-logs": "^6.14.0", "@hookform/resolvers": "^5.1.1", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.5", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.5", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.5", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-socket.io": "^11.1.5", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@types/bcryptjs": "^2.4.6", "@types/dd-trace": "^0.7.1", "@types/morgan": "^1.9.10", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.20", "bcryptjs": "^3.0.2", "bull": "^4.16.5", "cache-manager": "^7.0.1", "cache-manager-redis-store": "^2.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "compression": "^1.8.1", "dd-trace": "^5.60.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.23.6", "helmet": "^8.1.0", "ioredis": "^5.6.1", "lucide-react": "^0.468.0", "morgan": "^1.10.1", "next": "14.2.23", "next-themes": "^0.2.1", "openai": "^5.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "radix-ui": "^1.1.3", "react": "^18.3.1", "react-day-picker": "^9.5.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "redis": "^4.7.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "typeorm": "^0.3.25", "uuid": "^11.1.0", "vaul": "^1.1.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.76"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/testing": "^11.1.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "concurrently": "^9.2.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "jest": "^30.0.4", "lint-staged": "^15.5.2", "nodemon": "^3.1.10", "postcss": "^8", "prettier": "^3.6.2", "supertest": "^7.1.3", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "ts-jest": "^29.4.0", "typescript": "^5"}}